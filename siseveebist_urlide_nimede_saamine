# Config
$siteUrl = "https://pindikv.sharepoint.com/SitePages/Home.aspx"
$libraryName = "pkvh"
$parentFolderPath = "ATS-SE Aktid"  # Change to the main folder name
$outputFile = "/Users/<USER>/Documents/augment-projects/bulk qr/sharepoint_links.txt"

# Connect using Interactive with SharePoint Online Management Shell client ID
try {
    Write-Host "Attempting to connect to SharePoint..."
    Write-Host "This will open a browser window for authentication."

    # Use SharePoint Online Management Shell client ID (should be registered in most tenants)
    Connect-PnPOnline -Url $siteUrl -Interactive -ClientId "9bc3ab49-b65d-410a-85ad-de819febfddc"
    Write-Host "Successfully connected to SharePoint"
} catch {
    Write-Host "Failed to connect with SharePoint Online Management Shell client ID: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "Trying with PnP Office 365 Management Shell client ID..."

    try {
        # Try with PnP Office 365 Management Shell client ID
        Connect-PnPOnline -Url $siteUrl -Interactive -ClientId "1950a258-227b-4e31-a9cf-717495945fc2"
        Write-Host "Successfully connected using PnP Office 365 Management Shell"
    } catch {
        Write-Host "All connection methods failed."
        Write-Host "Error: $($_.Exception.Message)"
        Write-Host ""
        Write-Host "To resolve this issue, you need to:"
        Write-Host "1. Ask your SharePoint administrator to register the PnP PowerShell app in your tenant"
        Write-Host "2. Or create a custom Azure AD app registration with the required permissions"
        Write-Host "3. Site URL being used: $siteUrl"
        Write-Host ""
        Write-Host "For now, you can try running this script on a Windows machine with SharePoint Online Management Shell installed."
        exit 1
    }
}

# Get all subfolders inside the parent folder
$folders = Get-PnPFolderItem -FolderSiteRelativeUrl "$libraryName/$parentFolderPath" -ItemType Folder

# Prepare output
$output = @()

foreach ($folder in $folders) {
    $folderPath = "$libraryName/$parentFolderPath/$($folder.Name)"

    # Create or get anonymous sharing link
    $sharingLink = Grant-PnPFolderAccess -Identity $folderPath -LinkType View -Scope Anonymous | Select-Object -ExpandProperty Link

    # Format line
    $line = "$($sharingLink.WebUrl) - $($folder.Name)"
    $output += $line
}

# Write to file
$output | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Done. Links saved to $outputFile"