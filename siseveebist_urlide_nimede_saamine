# Config
$siteUrl = "https://pindikv.sharepoint.com/SitePages/Home.aspx"
$libraryName = "pkvh"
$parentFolderPath = "ATS-SE Aktid"  # Change to the main folder name
$outputFile = "/Users/<USER>/Documents/augment-projects/bulk qr/sharepoint_links.txt"

# Connect using credentials
try {
    Write-Host "Attempting to connect to SharePoint..."
    Write-Host "Please enter your SharePoint credentials when prompted."

    # Prompt for credentials
    $credential = Get-Credential -Message "Enter your SharePoint Online credentials"

    # Try to connect with credentials
    Connect-PnPOnline -Url $siteUrl -Credentials $credential
    Write-Host "Successfully connected to SharePoint"
} catch {
    Write-Host "Failed to connect with credentials: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "Trying alternative connection method..."

    try {
        # Try with current user (if running on domain-joined machine)
        Connect-PnPOnline -Url $siteUrl -CurrentCredentials
        Write-Host "Successfully connected using current credentials"
    } catch {
        Write-Host "All connection methods failed."
        Write-Host "Error: $($_.Exception.Message)"
        Write-Host ""
        Write-Host "Please ensure:"
        Write-Host "1. You have proper permissions to the SharePoint site"
        Write-Host "2. The site URL is correct: $siteUrl"
        Write-Host "3. Your credentials are valid"
        Write-Host "4. Multi-factor authentication is not required (or use app password)"
        exit 1
    }
}

# Get all subfolders inside the parent folder
$folders = Get-PnPFolderItem -FolderSiteRelativeUrl "$libraryName/$parentFolderPath" -ItemType Folder

# Prepare output
$output = @()

foreach ($folder in $folders) {
    $folderPath = "$libraryName/$parentFolderPath/$($folder.Name)"

    # Create or get anonymous sharing link
    $sharingLink = Grant-PnPFolderAccess -Identity $folderPath -LinkType View -Scope Anonymous | Select-Object -ExpandProperty Link

    # Format line
    $line = "$($sharingLink.WebUrl) - $($folder.Name)"
    $output += $line
}

# Write to file
$output | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Done. Links saved to $outputFile"