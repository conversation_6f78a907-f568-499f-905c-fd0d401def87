# Config
$siteUrl = "https://pindikv.sharepoint.com/SitePages/Home.aspx"
$libraryName = "pkvh"
$parentFolderPath = "ATS-SE Aktid"  # Change to the main folder name
$outputFile = "/Users/<USER>/Documents/augment-projects/bulk qr"

# Connect
Connect-PnPOnline -Url $siteUrl -Interactive

# Get all subfolders inside the parent folder
$folders = Get-PnPFolderItem -FolderSiteRelativeUrl "$libraryName/$parentFolderPath" -ItemType Folder

# Prepare output
$output = @()

foreach ($folder in $folders) {
    $folderPath = "$libraryName/$parentFolderPath/$($folder.Name)"

    # Create or get anonymous sharing link
    $sharingLink = Grant-PnPFolderAccess -Identity $folderPath -LinkType View -Scope Anonymous | Select-Object -ExpandProperty Link

    # Format line
    $line = "$($sharingLink.WebUrl) - $($folder.Name)"
    $output += $line
}

# Write to file
$output | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Done. Links saved to $outputFile"